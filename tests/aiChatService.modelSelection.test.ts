// AI聊天服务模型选择一致性测试 - 验证用户选择的模型与实际调用的模型一致

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIChatService } from '../src/services/aiChatService'
import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { aiProviderService } from '../src/services/aiProviderService'

// Mock依赖服务
vi.mock('../src/services/aiIntegrationService')
vi.mock('../src/services/aiProviderService')
vi.mock('../src/utils/chromeStorage')

const mockAiIntegrationService = aiIntegrationService as any
const mockAiProviderService = aiProviderService as any

describe('AIChatService - 模型选择和调用一致性修复', () => {
  let aiChatService: AIChatService

  beforeEach(() => {
    aiChatService = new AIChatService()
    vi.clearAllMocks()
  })

  describe('修复：用户选择的模型与实际调用的模型不一致', () => {
    const mockProvider = {
      id: 'lm-studio_1755596350020',
      name: '本地LM studio',
      type: 'lm-studio',
      enabled: true,
      baseUrl: 'http://localhost:1234/v1'
    }

    const mockProviders = [mockProvider]

    beforeEach(() => {
      mockAiIntegrationService.getConfiguredProviders.mockResolvedValue(mockProviders)
      mockAiProviderService.generateText.mockResolvedValue({
        success: true,
        content: '测试回复内容'
      })
    })

    it('应该使用context中指定的模型进行测试调用', async () => {
      // 模拟用户在界面上选择的模型
      const userSelectedModel = 'qwen/qwen3-30b-a3b'
      const userSelectedProvider = 'lm-studio_1755596350020'

      // 准备测试请求（模拟从AIIntegrationTab发送的测试请求）
      const testRequest = {
        prompt: '你好，请简单回复一下确认你能正常工作。',
        generationType: 'test' as const,
        context: {
          providerId: userSelectedProvider,
          modelId: userSelectedModel
        },
        maxLength: 100
      }

      // 执行测试
      const result = await aiChatService.generateText(testRequest)

      // 验证结果成功
      expect(result.content).toBe('测试回复内容')

      // 关键验证：确保调用了用户选择的模型，而不是默认模型
      expect(mockAiProviderService.generateText).toHaveBeenCalledWith({
        providerId: userSelectedProvider,
        modelId: userSelectedModel, // 这里必须是用户选择的模型
        messages: expect.arrayContaining([
          expect.objectContaining({
            role: 'system',
            content: expect.any(String)
          }),
          expect.objectContaining({
            role: 'user',
            content: '你好，请简单回复一下确认你能正常工作。'
          })
        ]),
        maxTokens: 50,
        temperature: 0.7
      })

      // 验证返回的元数据包含正确的模型信息
      expect(result.metadata?.model).toBe(userSelectedModel)
      expect(result.metadata?.provider).toBe('本地LM studio')
    })

    it('应该在没有context时回退到默认模型', async () => {
      // Mock默认模型
      const mockDefaultModel = {
        providerId: 'lm-studio_1755596350020',
        modelId: 'deepseek-r1-distill-qwen-32b' // 这是之前错误调用的模型
      }
      
      vi.spyOn(aiChatService as any, 'getDefaultModel').mockResolvedValue(mockDefaultModel)

      // 准备没有context的请求
      const requestWithoutContext = {
        prompt: '生成描述',
        generationType: 'description' as const,
        maxLength: 200
      }

      // 执行测试
      await aiChatService.generateText(requestWithoutContext)

      // 验证使用了默认模型
      expect(mockAiProviderService.generateText).toHaveBeenCalledWith({
        providerId: 'lm-studio_1755596350020',
        modelId: 'deepseek-r1-distill-qwen-32b',
        messages: expect.any(Array),
        maxTokens: 100,
        temperature: 0.7
      })
    })

    it('应该优先使用context中的模型而不是默认模型', async () => {
      // Mock默认模型（不同于context中的模型）
      const mockDefaultModel = {
        providerId: 'lm-studio_1755596350020',
        modelId: 'default-model-should-not-be-used'
      }
      
      vi.spyOn(aiChatService as any, 'getDefaultModel').mockResolvedValue(mockDefaultModel)

      // 准备有context的请求
      const requestWithContext = {
        prompt: '测试提示',
        generationType: 'test' as const,
        context: {
          providerId: 'lm-studio_1755596350020',
          modelId: 'qwen/qwen3-30b-a3b' // 用户选择的模型
        },
        maxLength: 100
      }

      // 执行测试
      await aiChatService.generateText(requestWithContext)

      // 验证使用了context中的模型，而不是默认模型
      expect(mockAiProviderService.generateText).toHaveBeenCalledWith({
        providerId: 'lm-studio_1755596350020',
        modelId: 'qwen/qwen3-30b-a3b', // 应该是用户选择的模型
        messages: expect.any(Array),
        maxTokens: 50,
        temperature: 0.7
      })

      // 验证没有调用getDefaultModel（因为有context）
      expect(aiChatService['getDefaultModel']).not.toHaveBeenCalled()
    })

    it('应该在context不完整时使用默认模型', async () => {
      // Mock默认模型
      const mockDefaultModel = {
        providerId: 'lm-studio_1755596350020',
        modelId: 'fallback-model'
      }
      
      vi.spyOn(aiChatService as any, 'getDefaultModel').mockResolvedValue(mockDefaultModel)

      // 准备context不完整的请求（只有providerId，没有modelId）
      const requestWithIncompleteContext = {
        prompt: '测试提示',
        generationType: 'test' as const,
        context: {
          providerId: 'lm-studio_1755596350020'
          // 缺少modelId
        },
        maxLength: 100
      }

      // 执行测试
      await aiChatService.generateText(requestWithIncompleteContext)

      // 验证使用了默认模型
      expect(mockAiProviderService.generateText).toHaveBeenCalledWith({
        providerId: 'lm-studio_1755596350020',
        modelId: 'fallback-model',
        messages: expect.any(Array),
        maxTokens: 50,
        temperature: 0.7
      })
    })

    it('应该记录正确的调试信息', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      const testRequest = {
        prompt: '测试',
        generationType: 'test' as const,
        context: {
          providerId: 'lm-studio_1755596350020',
          modelId: 'qwen/qwen3-30b-a3b'
        }
      }

      await aiChatService.generateText(testRequest)

      // 验证记录了正确的调试信息
      expect(consoleSpy).toHaveBeenCalledWith('使用context指定的模型:', {
        providerId: 'lm-studio_1755596350020',
        modelId: 'qwen/qwen3-30b-a3b'
      })
      expect(consoleSpy).toHaveBeenCalledWith('使用模型:', 'qwen/qwen3-30b-a3b')

      consoleSpy.mockRestore()
    })
  })
})
