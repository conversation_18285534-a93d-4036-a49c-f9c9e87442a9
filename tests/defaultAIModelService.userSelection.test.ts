// 默认AI模型服务用户选择功能测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { DefaultAIModelService } from '../src/services/defaultAIModelService'
import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock依赖服务
vi.mock('../src/services/aiIntegrationService')
vi.mock('../src/utils/chromeStorage')

const mockAiIntegrationService = aiIntegrationService as any
const mockChromeStorageService = ChromeStorageService as any

describe('DefaultAIModelService - 用户选择模型功能', () => {
  let defaultAIModelService: DefaultAIModelService

  beforeEach(() => {
    defaultAIModelService = new DefaultAIModelService()
    vi.clearAllMocks()
  })

  describe('setRecommendedConfigurationWithModels方法', () => {
    const mockProviders = [
      {
        id: 'lm-studio_1755596350020',
        name: '本地LM studio',
        type: 'lm-studio',
        enabled: true
      }
    ]

    const mockModels = [
      {
        id: 'qwen/qwen3-30b-a3b',
        name: 'qwen3-30b-a3b',
        displayName: 'Qwen3 30B A3B',
        isRecommended: true,
        isPopular: false
      },
      {
        id: 'deepseek-r1-distill-qwen-32b',
        name: 'deepseek-r1-distill-qwen-32b',
        displayName: 'DeepSeek R1 Distill Qwen 32B',
        isRecommended: false,
        isPopular: true
      }
    ]

    const mockAvailableModels = [
      {
        id: 'lm-studio_1755596350020_qwen/qwen3-30b-a3b',
        name: 'qwen3-30b-a3b',
        displayName: 'Qwen3 30B A3B',
        provider: '本地LM studio',
        providerId: 'lm-studio_1755596350020',
        modelType: 'chat',
        enabled: true,
        status: 'connected',
        isRecommended: true,
        isPopular: false
      },
      {
        id: 'lm-studio_1755596350020_deepseek-r1-distill-qwen-32b',
        name: 'deepseek-r1-distill-qwen-32b',
        displayName: 'DeepSeek R1 Distill Qwen 32B',
        provider: '本地LM studio',
        providerId: 'lm-studio_1755596350020',
        modelType: 'chat',
        enabled: true,
        status: 'connected',
        isRecommended: false,
        isPopular: true
      }
    ]

    const mockUsages = [
      {
        id: 'default-chat',
        name: '默认聊天',
        description: '通用对话和问答场景',
        category: 'chat',
        selectedModelId: null,
        fallbackModelId: null,
        enabled: true,
        priority: 1
      },
      {
        id: 'translation',
        name: '翻译',
        description: '文本翻译功能',
        category: 'translation',
        selectedModelId: null,
        fallbackModelId: null,
        enabled: true,
        priority: 2
      }
    ]

    beforeEach(() => {
      mockAiIntegrationService.getConfiguredProviders.mockResolvedValue(mockProviders)
      mockAiIntegrationService.testConnection.mockResolvedValue({ success: true })
      mockAiIntegrationService.getAvailableModels.mockResolvedValue(mockModels)
      mockChromeStorageService.getSyncSetting.mockResolvedValue(mockUsages)
      mockChromeStorageService.saveSyncSetting.mockResolvedValue(undefined)
    })

    it('应该使用用户选择的主要模型和备选模型设置配置', async () => {
      const primaryModelId = 'lm-studio_1755596350020_qwen/qwen3-30b-a3b'
      const fallbackModelId = 'lm-studio_1755596350020_deepseek-r1-distill-qwen-32b'

      await defaultAIModelService.setRecommendedConfigurationWithModels(primaryModelId, fallbackModelId)

      // 验证保存了正确的配置
      expect(mockChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'default_ai_models',
        expect.arrayContaining([
          expect.objectContaining({
            id: 'default-chat',
            selectedModelId: primaryModelId,
            fallbackModelId: fallbackModelId
          }),
          expect.objectContaining({
            id: 'translation',
            selectedModelId: primaryModelId,
            fallbackModelId: fallbackModelId
          })
        ])
      )
    })

    it('应该支持只设置主要模型，不设置备选模型', async () => {
      const primaryModelId = 'lm-studio_1755596350020_qwen/qwen3-30b-a3b'

      await defaultAIModelService.setRecommendedConfigurationWithModels(primaryModelId, null)

      // 验证保存了正确的配置
      expect(mockChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'default_ai_models',
        expect.arrayContaining([
          expect.objectContaining({
            id: 'default-chat',
            selectedModelId: primaryModelId,
            fallbackModelId: null
          }),
          expect.objectContaining({
            id: 'translation',
            selectedModelId: primaryModelId,
            fallbackModelId: null
          })
        ])
      )
    })

    it('应该在主要模型不存在时抛出错误', async () => {
      const invalidModelId = 'non-existent-model'

      await expect(
        defaultAIModelService.setRecommendedConfigurationWithModels(invalidModelId, null)
      ).rejects.toThrow('指定的主要模型不存在: non-existent-model')
    })

    it('应该在备选模型不存在时抛出错误', async () => {
      const primaryModelId = 'lm-studio_1755596350020_qwen/qwen3-30b-a3b'
      const invalidFallbackModelId = 'non-existent-fallback-model'

      await expect(
        defaultAIModelService.setRecommendedConfigurationWithModels(primaryModelId, invalidFallbackModelId)
      ).rejects.toThrow('指定的备选模型不存在: non-existent-fallback-model')
    })

    it('应该记录正确的日志信息', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      const primaryModelId = 'lm-studio_1755596350020_qwen/qwen3-30b-a3b'
      const fallbackModelId = 'lm-studio_1755596350020_deepseek-r1-distill-qwen-32b'

      await defaultAIModelService.setRecommendedConfigurationWithModels(primaryModelId, fallbackModelId)

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('推荐配置设置成功 - 主要模型: Qwen3 30B A3B, 备选模型: DeepSeek R1 Distill Qwen 32B')
      )

      consoleSpy.mockRestore()
    })

    it('应该在没有备选模型时记录正确的日志', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      const primaryModelId = 'lm-studio_1755596350020_qwen/qwen3-30b-a3b'

      await defaultAIModelService.setRecommendedConfigurationWithModels(primaryModelId, null)

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('推荐配置设置成功 - 主要模型: Qwen3 30B A3B, 备选模型: 无')
      )

      consoleSpy.mockRestore()
    })
  })
})
