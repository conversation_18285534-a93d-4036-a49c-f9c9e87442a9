import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { AIService } from '../src/services/aiService'

// Mock dependencies
vi.mock('../src/services/aiChatService', () => ({
  aiChatService: {
    generateText: vi.fn()
  }
}))

vi.mock('../src/services/tagService', () => ({
  tagService: {
    getTags: vi.fn()
  }
}))

vi.mock('../src/services/aiCacheService', () => ({
  aiCacheService: {
    getTagsCache: vi.fn(),
    saveTagsCache: vi.fn()
  }
}))

vi.mock('../src/services/aiConfigService', () => ({
  aiConfigService: {
    getConfig: vi.fn()
  }
}))

vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn()
  }
}))

describe('AI标签生成修复验证', () => {
  let aiService: AIService
  let aiChatService: any
  let tagService: any
  let aiCacheService: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // 动态导入服务
    const aiChatServiceModule = await import('../src/services/aiChatService')
    const tagServiceModule = await import('../src/services/tagService')
    const aiCacheServiceModule = await import('../src/services/aiCacheService')

    aiChatService = aiChatServiceModule.aiChatService
    tagService = tagServiceModule.tagService
    aiCacheService = aiCacheServiceModule.aiCacheService

    aiService = new AIService()

    // Mock基础配置
    vi.mocked(tagService.getTags).mockResolvedValue([
      { id: '1', name: '软件', color: '#blue', bookmarkCount: 5 },
      { id: '2', name: '开发资源', color: '#green', bookmarkCount: 3 },
      { id: '3', name: 'ai', color: '#purple', bookmarkCount: 2 }
    ])

    vi.mocked(aiCacheService.getTagsCache).mockResolvedValue(null)
    vi.mocked(aiCacheService.saveTagsCache).mockResolvedValue()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('AI响应过滤修复', () => {
    it('应该正确过滤包含<think>标签的响应并提取有效标签', async () => {
      // Mock AI响应，包含<think>标签和有效标签
      const mockResponse = `<think>
好的, 我现在需要处理用户的标签推荐请求。首先, 用户提供的信息是关于中国大学MOOC的在线课程学习平台, 网址是https://www.icourse163.org/, 但内容部分为空。用户要求生成4个相关标签, 并且必须优先从现有的标签库中选择, 现有标签库包括"软件"、"开发资源"、"ai"。
</think>

软件, 开发资源`

      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: mockResponse,
        metadata: { model: 'test-model', provider: 'test-provider', timestamp: new Date().toISOString() }
      })

      const request = {
        title: '中国大学MOOC_优质在线课程学习平台',
        url: 'https://www.icourse163.org/',
        content: '',
        description: '',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      // 验证结果
      expect(result.tags).toEqual(['软件', '开发资源'])
      expect(result.tags.length).toBeGreaterThan(0)
      expect(result.confidence).toBeGreaterThan(0)
    })

    it('应该处理不完整的<think>标签（没有结束标签）', async () => {
      const mockResponse = `<think>
用户要求生成标签，现有标签库包括"软件"、"开发资源"、"ai"。

软件, 开发资源, ai`

      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: mockResponse,
        metadata: { model: 'test-model', provider: 'test-provider', timestamp: new Date().toISOString() }
      })

      const request = {
        title: '开发工具网站',
        url: 'https://example.com',
        content: '开发工具和资源',
        maxTags: 3
      }

      const result = await aiService.generateTags(request)

      // 验证能够提取到标签
      expect(result.tags.length).toBeGreaterThan(0)
      expect(result.tags).toContain('软件')
      expect(result.tags).toContain('开发资源')
    })
  })

  describe('后台AI优化改进', () => {
    it('应该在AI结果有效时正确比较和更新', async () => {
      // 模拟降级策略先返回结果
      vi.mocked(tagService.getTags).mockResolvedValue([])
      
      // 第一次调用（超时，触发降级）
      let callCount = 0
      vi.mocked(aiChatService.generateText).mockImplementation(() => {
        callCount++
        if (callCount === 1) {
          // 第一次调用模拟超时
          return new Promise(() => {}) // 永不resolve
        } else {
          // 后台优化调用返回有效结果
          return Promise.resolve({
            content: '软件, 开发资源',
            metadata: { model: 'test-model', provider: 'test-provider', timestamp: new Date().toISOString() }
          })
        }
      })

      const request = {
        title: '软件开发平台',
        url: 'https://dev.example.com',
        content: '软件开发相关内容',
        maxTags: 5
      }

      // 开始AI标签生成
      const resultPromise = aiService.generateTags(request)

      // 快进45秒，触发降级策略
      vi.advanceTimersByTime(45000)

      const result = await resultPromise

      // 验证降级策略结果
      expect(result.reasoning).toContain('本地规则')
      
      // 等待后台优化完成
      vi.advanceTimersByTime(1000)
      await vi.runAllTimersAsync()

      // 验证没有抛出错误
      expect(true).toBe(true)
    })
  })

  describe('标签写入验证', () => {
    it('应该返回有效的标签数组而不是空数组', async () => {
      vi.mocked(aiChatService.generateText).mockResolvedValue({
        content: '网站, 学习, 教育',
        metadata: { model: 'test-model', provider: 'test-provider', timestamp: new Date().toISOString() }
      })

      const request = {
        title: '在线学习平台',
        url: 'https://learn.example.com',
        content: '在线教育和学习资源',
        maxTags: 5
      }

      const result = await aiService.generateTags(request)

      // 验证返回有效标签
      expect(result.tags).toEqual(['网站', '学习', '教育'])
      expect(result.tags.length).toBeGreaterThan(0)
      expect(result.confidence).toBeGreaterThan(0)
    })
  })
})
