// AI模型配置修复验证测试
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { aiChatService } from '../src/services/aiChatService'
import { DefaultAIModelAPI } from '../src/services/defaultAIModelAPI'
import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock dependencies
vi.mock('../src/services/defaultAIModelAPI')
vi.mock('../src/services/aiIntegrationService')
vi.mock('../src/utils/chromeStorage')

describe('AI模型配置修复验证', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Select组件空值修复', () => {
    it('应该使用null而不是空字符串作为fallback值', () => {
      // 这个测试验证DefaultAIModelsTab.tsx中的修复
      // 由于是React组件，我们主要验证逻辑正确性
      
      // 模拟selectedFallbackModel的处理逻辑
      const selectedFallbackModel: string | null = null
      const displayValue = selectedFallbackModel || 'none'
      const processedValue = displayValue === 'none' ? null : displayValue
      
      expect(displayValue).toBe('none')
      expect(processedValue).toBe(null)
      expect(processedValue).not.toBe('')
    })

    it('应该正确处理非空的fallback模型值', () => {
      const selectedFallbackModel = 'some-model-id'
      const displayValue = selectedFallbackModel || 'none'
      const processedValue = displayValue === 'none' ? null : displayValue
      
      expect(displayValue).toBe('some-model-id')
      expect(processedValue).toBe('some-model-id')
    })
  })

  describe('AI模型配置读取修复', () => {
    it('应该正确处理模型不存在的情况', async () => {
      // Mock默认模型返回
      const mockDefaultModel = {
        id: 'lm-studio_1755596350020_deepseek-r1-distill-qwen-32b',
        name: 'deepseek-r1-distill-qwen-32b',
        displayName: 'DeepSeek R1 Distill Qwen 32B',
        provider: '本地LM studio',
        providerId: 'lm-studio_1755596350020',
        modelType: 'chat' as const,
        enabled: true,
        status: 'connected' as const
      }

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel)

      // Mock提供商配置
      const mockProviders = [{
        id: 'lm-studio_1755596350020',
        name: '本地LM studio',
        type: 'lm-studio',
        enabled: true
      }]

      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)

      // Mock可用模型列表（不包含目标模型）
      const mockAvailableModels = [
        {
          id: 'qwen3-coder-30b',
          name: 'qwen3-coder-30b',
          displayName: 'Qwen 3 Coder 30B',
          isRecommended: true
        },
        {
          id: 'llama2-7b',
          name: 'llama2-7b', 
          displayName: 'Llama 2 7B',
          isRecommended: false
        }
      ]

      vi.mocked(aiIntegrationService.getAvailableModels).mockResolvedValue(mockAvailableModels)

      // Mock setDefaultModel方法
      const setDefaultModelSpy = vi.spyOn(aiChatService, 'setDefaultModel').mockResolvedValue()

      // Mock callAIProvider方法
      const mockResponse = {
        content: '测试响应',
        metadata: {
          model: 'qwen3-coder-30b',
          provider: '本地LM studio'
        }
      }
      
      vi.spyOn(aiChatService as any, 'callAIProvider').mockResolvedValue(mockResponse)
      vi.spyOn(aiChatService as any, 'parseAIResponse').mockReturnValue({
        content: '测试响应',
        suggestions: []
      })
      vi.spyOn(aiChatService as any, 'buildSystemPrompt').mockReturnValue('系统提示词')

      // 执行测试
      const request = {
        prompt: '测试提示词',
        generationType: 'tags' as const
      }

      const result = await aiChatService.generateText(request)

      // 验证结果
      expect(result).toBeDefined()
      expect(result.content).toBe('测试响应')
      expect(result.metadata?.model).toBe('qwen3-coder-30b')

      // 验证降级逻辑被调用
      expect(setDefaultModelSpy).toHaveBeenCalledWith('lm-studio_1755596350020', 'qwen3-coder-30b')
    })

    it('应该正确解析模型ID格式', async () => {
      const mockDefaultModel = {
        id: 'lm-studio_1755596350020_qwen3-coder-30b',
        providerId: 'lm-studio_1755596350020'
      }

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel as any)

      // 验证ID解析逻辑 - 修复后应该使用providerId字段而不是解析id
      const [providerId, modelId] = mockDefaultModel.id.split('_', 2)

      expect(providerId).toBe('lm-studio')
      expect(modelId).toBe('1755596350020')

      // 修复后的逻辑应该直接使用providerId字段
      expect(mockDefaultModel.providerId).toBe('lm-studio_1755596350020')
    })

    it('应该在模型验证失败时继续使用原模型', async () => {
      const mockDefaultModel = {
        id: 'provider_model',
        providerId: 'test-provider'
      }

      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockDefaultModel as any)

      const mockProviders = [{
        id: 'test-provider',
        name: 'Test Provider',
        enabled: true
      }]

      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue(mockProviders)

      // Mock模型验证失败
      vi.mocked(aiIntegrationService.getAvailableModels).mockRejectedValue(new Error('验证失败'))

      // Mock其他方法
      vi.spyOn(aiChatService as any, 'callAIProvider').mockResolvedValue({ content: '测试' })
      vi.spyOn(aiChatService as any, 'parseAIResponse').mockReturnValue({ content: '测试', suggestions: [] })
      vi.spyOn(aiChatService as any, 'buildSystemPrompt').mockReturnValue('系统提示词')

      const request = {
        prompt: '测试',
        generationType: 'tags' as const
      }

      // 应该不抛出错误，继续使用原模型
      const result = await aiChatService.generateText(request)
      expect(result).toBeDefined()
    })
  })

  describe('错误处理改进', () => {
    it('应该提供清晰的错误信息', async () => {
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      vi.mocked(aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      const request = {
        prompt: '测试提示词',
        generationType: 'description' as const
      }

      await expect(aiChatService.generateText(request)).rejects.toThrow(
        '未找到可用的AI模型，请先在"默认AI模型"页面配置模型'
      )
    })
  })
})
