# 更新日志

本文档记录了Universe Bag（乾坤袋）扩展的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [2.0.0] - 2025-01-20

### 🎉 重大更新：AI模型管理功能全面优化

这是一个重要的功能更新版本，专注于改进AI模型管理体验，解决了用户反馈的核心问题。

### ✨ 新功能

#### AI模型管理优化
- **智能模型过滤**：默认AI设置页面现在只显示在AI集成页面中已添加并选择的模型
- **供应商名称自动识别**：根据提供商类型（OpenAI、Claude、Ollama等）自动填入默认名称
- **实时数据同步**：AI集成页面和默认AI设置页面之间建立了自动同步机制
- **用户引导优化**：添加了清晰的操作提示和引导信息

#### 用户体验改进
- **智能提示系统**：当没有可用模型时，显示详细的操作步骤指导
- **简化操作流程**：减少重复输入，提高配置效率
- **界面一致性**：确保两个页面显示的模型数据完全一致
- **错误预防**：避免显示无效或不可用的模型选项

### 🔧 技术改进

#### 架构优化
- **事件驱动同步**：使用CustomEvent机制实现服务间通信，避免循环依赖
- **数据一致性保证**：建立了完善的数据同步和验证机制
- **性能优化**：使用延迟执行和事件去重，减少不必要的计算
- **代码质量提升**：改进了错误处理和日志记录

#### 存储管理
- **统一数据源**：整合了AI模型相关的存储键管理
- **自动清理机制**：删除提供商时自动清理相关配置
- **数据验证**：增强了数据完整性检查

### 🐛 问题修复

#### 数据一致性问题
- **修复**：默认AI设置页面显示未选择模型的问题
- **修复**：删除AI提供商后默认设置未同步更新的问题
- **修复**：两个页面模型数据不一致的问题
- **修复**：供应商名称需要重复输入的问题

#### 用户体验问题
- **修复**：缺乏操作引导导致用户困惑的问题
- **修复**：界面提示信息不清晰的问题
- **修复**：模型选择后无法在默认设置中找到的问题

### 📋 详细变更

#### 核心文件修改
- `src/services/defaultAIModelService.ts` - 重构模型过滤和同步逻辑
- `src/services/aiIntegrationService.ts` - 新增事件驱动同步机制
- `src/components/DefaultAIModelsTab.tsx` - 添加用户引导和界面优化
- `src/components/AIIntegrationTab.tsx` - 实现供应商名称自动识别
- `src/components/ModelSelector.tsx` - 增强模型选择提示信息

#### 测试和文档
- 新增 `tests/ai-model-management-test.ts` 自动化测试
- 新增 `tests/ai-model-management-demo.html` 可视化测试页面
- 新增 `docs/ai-model-management-optimization.md` 技术文档

### 🔄 迁移指南

#### 对现有用户的影响
- **无需手动操作**：所有改进都是向后兼容的
- **自动数据迁移**：现有配置会自动适配新的同步机制
- **体验提升**：用户将立即感受到更流畅的操作体验

### 🎯 下一步计划
- AI模型性能监控和使用统计
- 更多AI提供商支持
- 高级模型配置选项
- 批量操作功能

---

## [1.1.0] - 2024-12-15

### ✨ 新增功能

#### 关于我们页面
- 自动从 manifest.json 获取扩展信息
- 显示开发者信息和联系方式
- 展示技术信息和运行环境状态
- 列出扩展权限详情和说明
- 显示许可证信息和相关链接

#### 帮助中心页面
- 分类浏览帮助内容（使用指南、常见问题、故障排除）
- 全文搜索功能，支持关键词匹配和模糊搜索
- 搜索建议和历史记录功能
- 内容展开/折叠交互
- 批量展开/折叠所有内容
- 快速导航锚点功能
- URL 锚点支持，可直接链接到特定章节
- 联系支持信息展示

#### 主题切换功能
- 支持浅色、深色和系统主题
- 主题状态持久化保存
  - 自动跟随系统主题变化

- 响应式设计
  - 移动端适配优化
  - 平板设备布局调整
  - 桌面端多列布局
  - 触摸设备交互优化

- 性能优化
  - 帮助内容懒加载
  - 搜索结果缓存机制
  - 组件渲染优化
  - 内存使用监控
  - Web Vitals 性能指标监控

- 错误处理
  - 页面级错误边界组件
  - 数据加载失败降级处理
  - 用户友好的错误提示
  - 错误恢复和重试机制

- 无障碍访问
  - 键盘导航支持
  - 屏幕阅读器兼容
  - ARIA 标签完善
  - 焦点管理优化

- 国际化准备
  - 中文界面和内容
  - 多语言支持架构
  - 本地化配置管理

### 改进
- 导航系统
  - 新增关于我们和帮助中心导航选项
  - 支持 Alt + 数字键快捷键切换
  - URL hash 路由支持
  - 浏览器前进后退支持
  - 导航状态持久化

- 用户体验
  - 统一的加载状态指示
  - 平滑的页面切换动画
  - 一致的视觉设计风格
  - 改进的交互反馈

### 技术改进
- 新增自定义 Hooks
  - `useTheme`: 主题管理
  - `useResponsive`: 响应式断点管理
  - `useLazyLoad`: 懒加载功能
  - `useCache`: 缓存管理

- 新增工具函数
  - `manifestReader`: manifest.json 读取和解析
  - `helpSearch`: 帮助内容搜索算法
  - `performance`: 性能监控和分析

- 测试覆盖
  - 单元测试覆盖率 > 85%
  - 集成测试覆盖主要用户流程
  - 端到端测试覆盖关键功能
  - 性能测试和稳定性测试

### 文档
- 完整的功能使用文档
- 代码规范和最佳实践指南
- 维护和故障排除指南
- API 文档和类型定义

## [1.0.0] - 2024-01-01

### 新增
- 初始版本发布
- 基础的选项页面框架
- 收藏管理功能
- 分类和标签管理
- 导入导出功能

---

## 版本说明

### 版本号规则
- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 更改类型
- **新增**：新功能
- **改进**：对现有功能的改进
- **修复**：问题修复
- **移除**：移除的功能
- **安全**：安全相关的修复
- **废弃**：即将移除的功能

### 发布流程
1. 更新版本号
2. 更新 CHANGELOG.md
3. 运行完整测试套件
4. 创建发布标签
5. 发布到扩展商店

### 兼容性说明
- 向下兼容：新版本可以处理旧版本的数据
- API 稳定性：公共 API 在主版本内保持稳定
- 数据迁移：提供自动数据迁移机制

### 已知问题
- 在某些旧版本浏览器中主题切换可能不生效
- 大量帮助内容时搜索性能可能下降
- 移动端某些交互需要进一步优化

### 计划功能
- 多语言支持（英文、日文等）
- 帮助内容的在线更新
- 用户反馈收集系统
- 高级搜索功能（正则表达式、标签筛选等）
- 帮助内容的导出功能
- 自定义主题颜色
- 键盘快捷键自定义

### 贡献者
感谢所有为此功能做出贡献的开发者和测试人员。

### 反馈和支持
如果您遇到问题或有功能建议，请通过以下方式联系我们：
- 邮箱：<EMAIL>
- 网站：https://universebag.com
- GitHub Issues：https://github.com/universebag/extension/issues