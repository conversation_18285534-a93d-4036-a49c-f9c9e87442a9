/**
 * AI推荐功能修复测试脚本
 * 测试智能推荐功能是否正确调用默认AI模型并生成真实内容
 */

const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  colors: {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
  },
  icons: {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    test: '🧪',
    fix: '🔧'
  }
}

/**
 * 彩色日志输出
 */
function log(message, color = 'reset') {
  console.log(`${TEST_CONFIG.colors[color]}${message}${TEST_CONFIG.colors.reset}`)
}

/**
 * 测试结果统计
 */
let testStats = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0
}

/**
 * 执行测试用例
 */
function runTest(testName, testFn) {
  testStats.total++
  try {
    log(`\n${TEST_CONFIG.icons.test} 测试: ${testName}`, 'cyan')
    const result = testFn()
    if (result === true || result === undefined) {
      testStats.passed++
      log(`${TEST_CONFIG.icons.success} 通过: ${testName}`, 'green')
      return true
    } else {
      testStats.failed++
      log(`${TEST_CONFIG.icons.error} 失败: ${testName} - ${result}`, 'red')
      return false
    }
  } catch (error) {
    testStats.failed++
    log(`${TEST_CONFIG.icons.error} 异常: ${testName} - ${error.message}`, 'red')
    return false
  }
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  return runTest(`检查${description}文件存在`, () => {
    if (!fs.existsSync(filePath)) {
      return `文件不存在: ${filePath}`
    }
    return true
  })
}

/**
 * 检查文件内容包含特定字符串
 */
function checkFileContains(filePath, searchString, description) {
  return runTest(`检查${description}`, () => {
    if (!fs.existsSync(filePath)) {
      return `文件不存在: ${filePath}`
    }
    
    const content = fs.readFileSync(filePath, 'utf8')
    if (!content.includes(searchString)) {
      return `文件中未找到: ${searchString}`
    }
    return true
  })
}

/**
 * 检查aiService.ts是否使用新的aiChatService
 */
function testAiServiceFix() {
  const aiServicePath = path.join(__dirname, '../src/services/aiService.ts')
  
  // 检查文件存在
  if (!checkFileExists(aiServicePath, 'aiService.ts')) {
    return false
  }
  
  // 检查是否使用aiChatService
  checkFileContains(aiServicePath, 'aiChatService.generateText', 'aiService使用aiChatService.generateText')
  
  // 检查generateCategory方法是否更新
  checkFileContains(aiServicePath, 'generationType: \'category\'', 'generateCategory使用新的生成类型')
  
  // 检查generateDescription方法是否更新
  checkFileContains(aiServicePath, 'generationType: \'description\'', 'generateDescription使用新的生成类型')
  
  // 检查是否移除了旧的API调用
  runTest('检查是否移除旧的callAIServiceWithRetry调用', () => {
    const content = fs.readFileSync(aiServicePath, 'utf8')
    const oldCallCount = (content.match(/callAIServiceWithRetry/g) || []).length
    if (oldCallCount > 1) { // 方法定义本身还在，但调用应该减少
      return `仍有${oldCallCount}处使用旧的API调用方式`
    }
    return true
  })
}

/**
 * 检查AIRecommendations组件是否添加了描述生成功能
 */
function testAIRecommendationsEnhancement() {
  const aiRecommendationsPath = path.join(__dirname, '../src/components/AIRecommendations.tsx')
  
  // 检查文件存在
  if (!checkFileExists(aiRecommendationsPath, 'AIRecommendations.tsx')) {
    return false
  }
  
  // 检查描述生成接口
  checkFileContains(aiRecommendationsPath, 'DescriptionGenerationResponse', '描述生成响应接口')
  
  // 检查描述生成状态
  checkFileContains(aiRecommendationsPath, 'descriptionGeneration', '描述生成状态')
  
  // 检查描述生成函数
  checkFileContains(aiRecommendationsPath, 'generateDescription', '描述生成函数')
  
  // 检查描述选择回调
  checkFileContains(aiRecommendationsPath, 'onDescriptionSelect', '描述选择回调')
  
  // 检查AI_GENERATE_DESCRIPTION消息
  checkFileContains(aiRecommendationsPath, 'AI_GENERATE_DESCRIPTION', 'AI描述生成消息类型')
  
  // 检查描述生成UI
  checkFileContains(aiRecommendationsPath, 'showDescriptionGeneration', '描述生成UI显示控制')
}

/**
 * 检查DetailedBookmarkForm是否正确集成新功能
 */
function testDetailedBookmarkFormIntegration() {
  const formPath = path.join(__dirname, '../src/popup/components/DetailedBookmarkForm.tsx')
  
  // 检查文件存在
  if (!checkFileExists(formPath, 'DetailedBookmarkForm.tsx')) {
    return false
  }
  
  // 检查描述选择处理函数
  checkFileContains(formPath, 'handleAIDescriptionSelect', '描述选择处理函数')
  
  // 检查AIRecommendations组件的新属性
  checkFileContains(formPath, 'onDescriptionSelect={handleAIDescriptionSelect}', 'AIRecommendations描述选择回调')
  checkFileContains(formPath, 'showDescriptionGeneration={true}', 'AIRecommendations描述生成显示')
  checkFileContains(formPath, 'currentDescription={watchedValues.description}', 'AIRecommendations当前描述')
  
  // 检查现有的AI生成按钮
  checkFileContains(formPath, 'AI_GENERATE_TAGS', '标签AI生成按钮')
  checkFileContains(formPath, 'AI_GENERATE_CATEGORY', '分类AI生成按钮')
  
  // 检查AITextGenerator组件使用
  checkFileContains(formPath, 'AITextGenerator', 'AITextGenerator组件使用')
}

/**
 * 检查BookmarkEditModal是否正确集成新功能
 */
function testBookmarkEditModalIntegration() {
  const modalPath = path.join(__dirname, '../src/components/BookmarkEditModal.tsx')
  
  // 检查文件存在
  if (!checkFileExists(modalPath, 'BookmarkEditModal.tsx')) {
    return false
  }
  
  // 检查描述选择处理函数
  checkFileContains(modalPath, 'handleAIDescriptionSelect', '编辑模态框描述选择处理函数')
  
  // 检查AIRecommendations组件的新属性
  checkFileContains(modalPath, 'onDescriptionSelect={handleAIDescriptionSelect}', '编辑模态框AIRecommendations描述选择回调')
  checkFileContains(modalPath, 'showDescriptionGeneration={true}', '编辑模态框AIRecommendations描述生成显示')
}

/**
 * 检查消息处理器是否支持新功能
 */
function testMessageHandlerSupport() {
  const handlerPath = path.join(__dirname, '../src/background/messageHandler.ts')
  
  // 检查文件存在
  if (!checkFileExists(handlerPath, 'messageHandler.ts')) {
    return false
  }
  
  // 检查AI_GENERATE_DESCRIPTION处理器注册
  checkFileContains(handlerPath, '\'AI_GENERATE_DESCRIPTION\': this.handleAIGenerateDescription.bind(this)', 'AI描述生成处理器注册')
  
  // 检查处理器实现
  checkFileContains(handlerPath, 'handleAIGenerateDescription', 'AI描述生成处理器实现')
  
  // 检查AI推荐处理器
  checkFileContains(handlerPath, 'handleAIRecommendBoth', 'AI批量推荐处理器')
}

/**
 * 检查构建产物
 */
function testBuildOutput() {
  const distPath = path.join(__dirname, '../dist')
  
  // 检查dist目录存在
  if (!checkFileExists(distPath, 'dist目录')) {
    return false
  }
  
  // 检查关键文件
  checkFileExists(path.join(distPath, 'manifest.json'), 'manifest.json')
  checkFileExists(path.join(distPath, 'src/background/index.js'), 'background script')
  checkFileExists(path.join(distPath, 'src/options/index.html'), 'options页面')
  
  // 检查构建的JavaScript文件大小合理
  runTest('检查构建文件大小合理', () => {
    const backgroundPath = path.join(distPath, 'src/background/index.js')
    if (fs.existsSync(backgroundPath)) {
      const stats = fs.statSync(backgroundPath)
      if (stats.size < 1000) {
        return `background script文件过小: ${stats.size} bytes`
      }
      if (stats.size > 50 * 1024 * 1024) {
        return `background script文件过大: ${stats.size} bytes`
      }
    }
    return true
  })
}

/**
 * 主测试函数
 */
function runAllTests() {
  log(`\n${TEST_CONFIG.icons.fix} AI推荐功能修复测试开始`, 'bright')
  log('=' * 60, 'cyan')
  
  // 1. 测试aiService.ts修复
  log(`\n📋 测试组 1: aiService.ts修复`, 'yellow')
  testAiServiceFix()
  
  // 2. 测试AIRecommendations组件增强
  log(`\n📋 测试组 2: AIRecommendations组件增强`, 'yellow')
  testAIRecommendationsEnhancement()
  
  // 3. 测试DetailedBookmarkForm集成
  log(`\n📋 测试组 3: DetailedBookmarkForm集成`, 'yellow')
  testDetailedBookmarkFormIntegration()
  
  // 4. 测试BookmarkEditModal集成
  log(`\n📋 测试组 4: BookmarkEditModal集成`, 'yellow')
  testBookmarkEditModalIntegration()
  
  // 5. 测试消息处理器支持
  log(`\n📋 测试组 5: 消息处理器支持`, 'yellow')
  testMessageHandlerSupport()
  
  // 6. 测试构建产物
  log(`\n📋 测试组 6: 构建产物验证`, 'yellow')
  testBuildOutput()
  
  // 输出测试结果
  log('\n' + '=' * 60, 'cyan')
  log(`📊 测试结果统计:`, 'bright')
  log(`   总计: ${testStats.total}`, 'blue')
  log(`   通过: ${testStats.passed}`, 'green')
  log(`   失败: ${testStats.failed}`, 'red')
  log(`   警告: ${testStats.warnings}`, 'yellow')
  
  const successRate = ((testStats.passed / testStats.total) * 100).toFixed(1)
  log(`   成功率: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red')
  
  if (testStats.failed === 0) {
    log(`\n${TEST_CONFIG.icons.success} 所有测试通过！AI推荐功能修复成功。`, 'green')
  } else {
    log(`\n${TEST_CONFIG.icons.error} 有 ${testStats.failed} 个测试失败，需要进一步检查。`, 'red')
  }
  
  return testStats.failed === 0
}

// 执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  runAllTests,
  testAiServiceFix,
  testAIRecommendationsEnhancement,
  testDetailedBookmarkFormIntegration,
  testBookmarkEditModalIntegration,
  testMessageHandlerSupport,
  testBuildOutput
}
