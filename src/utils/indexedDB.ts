import {
  Bookmark,
  Category,
  Tag,
  BookmarkFilter,
  SortOptions
} from '../types'
import { fallbackStorageService } from './fallbackStorage'

/**
 * IndexedDB数据库配置
 */
const DB_NAME = 'UniverseBagDB'
const DB_VERSION = 2

// 对象存储名称
const STORES = {
  BOOKMARKS: 'bookmarks',
  CATEGORIES: 'categories',
  TAGS: 'tags',
  SETTINGS: 'settings',
  AI_CACHE: 'ai_cache'
} as const

/**
 * IndexedDB存储服务类
 * 提供数据的持久化存储和检索功能
 */
export class IndexedDBService {
  private db: IDBDatabase | null = null
  private initPromise: Promise<void> | null = null
  private useFallback: boolean = false

  /**
   * 初始化数据库连接
   * @returns Promise<void>
   */
  async init(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = new Promise((resolve, reject) => {
      // 检查IndexedDB是否可用
      if (typeof indexedDB === 'undefined') {
        console.warn('IndexedDB不可用，使用降级存储')
        this.useFallback = true
        resolve()
        return
      }

      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        const error = request.error
        console.error('IndexedDB打开失败，使用降级存储:', error)
        this.useFallback = true
        resolve() // 不抛出错误，而是使用降级存储
      }

      request.onsuccess = () => {
        this.db = request.result
        this.useFallback = false

        // 添加数据库错误监听
        this.db.onerror = (event) => {
          console.error('IndexedDB运行时错误:', event)
        }

        console.log('IndexedDB数据库初始化成功')
        resolve()
      }

      request.onupgradeneeded = (event) => {
        try {
          const db = (event.target as IDBOpenDBRequest).result
          this.createObjectStores(db)
          console.log('IndexedDB数据库升级完成')
        } catch (error) {
          console.error('IndexedDB升级失败，使用降级存储:', error)
          this.useFallback = true
          resolve() // 不抛出错误，而是使用降级存储
        }
      }

      request.onblocked = () => {
        console.warn('IndexedDB升级被阻塞，使用降级存储')
        this.useFallback = true
        resolve() // 不抛出错误，而是使用降级存储
      }
    })

    return this.initPromise
  }

  /**
   * 创建对象存储
   * @param db 数据库实例
   */
  private createObjectStores(db: IDBDatabase): void {
    // 创建书签存储
    if (!db.objectStoreNames.contains(STORES.BOOKMARKS)) {
      const bookmarkStore = db.createObjectStore(STORES.BOOKMARKS, { keyPath: 'id' })
      bookmarkStore.createIndex('type', 'type', { unique: false })
      bookmarkStore.createIndex('category', 'category', { unique: false })
      bookmarkStore.createIndex('tags', 'tags', { unique: false, multiEntry: true })
      bookmarkStore.createIndex('createdAt', 'createdAt', { unique: false })
      bookmarkStore.createIndex('updatedAt', 'updatedAt', { unique: false })
      bookmarkStore.createIndex('title', 'title', { unique: false })
      bookmarkStore.createIndex('url', 'url', { unique: false })
    }

    // 创建分类存储
    if (!db.objectStoreNames.contains(STORES.CATEGORIES)) {
      const categoryStore = db.createObjectStore(STORES.CATEGORIES, { keyPath: 'id' })
      categoryStore.createIndex('name', 'name', { unique: true })
      categoryStore.createIndex('parentId', 'parentId', { unique: false })
      categoryStore.createIndex('createdAt', 'createdAt', { unique: false })
    }

    // 创建标签存储
    if (!db.objectStoreNames.contains(STORES.TAGS)) {
      const tagStore = db.createObjectStore(STORES.TAGS, { keyPath: 'id' })
      tagStore.createIndex('name', 'name', { unique: true })
      tagStore.createIndex('usageCount', 'usageCount', { unique: false })
      tagStore.createIndex('createdAt', 'createdAt', { unique: false })
    }

    // 创建设置存储
    if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
      db.createObjectStore(STORES.SETTINGS, { keyPath: 'key' })
    }

    // 创建AI缓存存储
    if (!db.objectStoreNames.contains(STORES.AI_CACHE)) {
      const aiCacheStore = db.createObjectStore(STORES.AI_CACHE, { keyPath: 'id' })
      aiCacheStore.createIndex('requestHash', 'requestHash', { unique: false })
      aiCacheStore.createIndex('createdAt', 'createdAt', { unique: false })
      aiCacheStore.createIndex('expiresAt', 'expiresAt', { unique: false })
      aiCacheStore.createIndex('hitCount', 'hitCount', { unique: false })
    }

    console.log('IndexedDB对象存储创建完成')
  }

  /**
   * 确保数据库已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.db && !this.useFallback) {
      await this.init()
    }
  }

  /**
   * 获取事务
   * @param storeNames 存储名称
   * @param mode 事务模式
   * @returns IDBTransaction
   */
  private getTransaction(storeNames: string | string[], mode: IDBTransactionMode = 'readonly'): IDBTransaction {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }
    return this.db.transaction(storeNames, mode)
  }

  // ==================== 书签操作 ====================

  /**
   * 保存书签
   * @param bookmark 书签对象
   * @returns Promise<string> 书签ID
   */
  async saveBookmark(bookmark: Bookmark): Promise<string> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.saveBookmark(bookmark)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.BOOKMARKS, 'readwrite')
      const store = transaction.objectStore(STORES.BOOKMARKS)

      const request = store.put(bookmark)

      request.onsuccess = () => {
        resolve(bookmark.id)
      }

      request.onerror = () => {
        reject(new Error(`保存书签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取书签
   * @param id 书签ID
   * @returns Promise<Bookmark | null>
   */
  async getBookmark(id: string): Promise<Bookmark | null> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.getBookmark(id)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.BOOKMARKS)
      const store = transaction.objectStore(STORES.BOOKMARKS)

      const request = store.get(id)

      request.onsuccess = () => {
        const result = request.result
        if (result) {
          // 确保日期字段被正确反序列化
          result.createdAt = new Date(result.createdAt)
          result.updatedAt = new Date(result.updatedAt)
          if (result.metadata?.publishDate) {
            result.metadata.publishDate = new Date(result.metadata.publishDate)
          }
        }
        resolve(result || null)
      }

      request.onerror = () => {
        reject(new Error(`获取书签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取所有书签
   * @param filter 筛选条件
   * @param sort 排序选项
   * @returns Promise<Bookmark[]>
   */
  async getBookmarks(filter?: BookmarkFilter, sort?: SortOptions): Promise<Bookmark[]> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.getBookmarks(filter, sort)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.BOOKMARKS)
      const store = transaction.objectStore(STORES.BOOKMARKS)

      let request: IDBRequest

      // 根据筛选条件选择索引
      if (filter?.categories && filter.categories.length > 0) {
        const index = store.index('category')
        request = index.getAll(filter.categories[0]) // 简化实现，只支持单个分类筛选
      } else if (filter?.type) {
        const index = store.index('type')
        request = index.getAll(filter.type)
      } else {
        request = store.getAll()
      }

      request.onsuccess = () => {
        let results: Bookmark[] = request.result || []

        // 确保所有书签的日期字段被正确反序列化
        results = results.map(bookmark => {
          bookmark.createdAt = new Date(bookmark.createdAt)
          bookmark.updatedAt = new Date(bookmark.updatedAt)
          if (bookmark.metadata?.publishDate) {
            bookmark.metadata.publishDate = new Date(bookmark.metadata.publishDate)
          }
          return bookmark
        })

        // 应用筛选
        if (filter) {
          results = this.applyBookmarkFilter(results, filter)
        }

        // 应用排序
        if (sort) {
          results = this.sortBookmarks(results, sort)
        }

        // 应用分页
        if (filter?.limit) {
          const offset = filter.offset || 0
          results = results.slice(offset, offset + filter.limit)
        }

        resolve(results)
      }

      request.onerror = () => {
        reject(new Error(`获取书签列表失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 更新书签
   * @param id 书签ID
   * @param updates 更新数据
   * @returns Promise<void>
   */
  async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<void> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.updateBookmark(id, updates)
    }

    const bookmark = await this.getBookmark(id)
    if (!bookmark) {
      throw new Error(`书签不存在: ${id}`)
    }

    const updatedBookmark = {
      ...bookmark,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: new Date()
    }

    await this.saveBookmark(updatedBookmark)
  }

  /**
   * 删除书签
   * @param id 书签ID
   * @returns Promise<void>
   */
  async deleteBookmark(id: string): Promise<void> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.deleteBookmark(id)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.BOOKMARKS, 'readwrite')
      const store = transaction.objectStore(STORES.BOOKMARKS)

      const request = store.delete(id)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`删除书签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 批量删除书签
   * @param ids 书签ID数组
   * @returns Promise<void>
   */
  async deleteBookmarks(ids: string[]): Promise<void> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.BOOKMARKS, 'readwrite')
      const store = transaction.objectStore(STORES.BOOKMARKS)

      let completed = 0
      let hasError = false

      const checkComplete = () => {
        completed++
        if (completed === ids.length) {
          if (hasError) {
            reject(new Error('批量删除书签时发生错误'))
          } else {
            resolve()
          }
        }
      }

      ids.forEach(id => {
        const request = store.delete(id)
        request.onsuccess = checkComplete
        request.onerror = () => {
          hasError = true
          checkComplete()
        }
      })
    })
  }

  // ==================== 分类操作 ====================

  /**
   * 保存分类
   * @param category 分类对象
   * @returns Promise<string> 分类ID
   */
  async saveCategory(category: Category): Promise<string> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.saveCategory(category)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.CATEGORIES, 'readwrite')
      const store = transaction.objectStore(STORES.CATEGORIES)

      const request = store.put(category)

      request.onsuccess = () => {
        resolve(category.id)
      }

      request.onerror = () => {
        reject(new Error(`保存分类失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取分类
   * @param id 分类ID
   * @returns Promise<Category | null>
   */
  async getCategory(id: string): Promise<Category | null> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.CATEGORIES)
      const store = transaction.objectStore(STORES.CATEGORIES)

      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => {
        reject(new Error(`获取分类失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取所有分类
   * @returns Promise<Category[]>
   */
  async getCategories(): Promise<Category[]> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.getCategories()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.CATEGORIES)
      const store = transaction.objectStore(STORES.CATEGORIES)

      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result || [])
      }

      request.onerror = () => {
        reject(new Error(`获取分类列表失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 更新分类
   * @param id 分类ID
   * @param updates 更新数据
   * @returns Promise<void>
   */
  async updateCategory(id: string, updates: Partial<Category>): Promise<void> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.updateCategory(id, updates)
    }

    const category = await this.getCategory(id)
    if (!category) {
      throw new Error(`分类不存在: ${id}`)
    }

    const updatedCategory = {
      ...category,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: new Date()
    }

    await this.saveCategory(updatedCategory)
  }

  /**
   * 删除分类
   * @param id 分类ID
   * @returns Promise<void>
   */
  async deleteCategory(id: string): Promise<void> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.CATEGORIES, 'readwrite')
      const store = transaction.objectStore(STORES.CATEGORIES)

      const request = store.delete(id)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`删除分类失败: ${request.error?.message}`))
      }
    })
  }

  // ==================== 标签操作 ====================

  /**
   * 保存标签
   * @param tag 标签对象
   * @returns Promise<string> 标签ID
   */
  async saveTag(tag: Tag): Promise<string> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.saveTag(tag)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.TAGS, 'readwrite')
      const store = transaction.objectStore(STORES.TAGS)

      const request = store.put(tag)

      request.onsuccess = () => {
        resolve(tag.id)
      }

      request.onerror = () => {
        reject(new Error(`保存标签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取标签
   * @param id 标签ID
   * @returns Promise<Tag | null>
   */
  async getTag(id: string): Promise<Tag | null> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.TAGS)
      const store = transaction.objectStore(STORES.TAGS)

      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => {
        reject(new Error(`获取标签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 根据名称获取标签
   * @param name 标签名称
   * @returns Promise<Tag | null>
   */
  async getTagByName(name: string): Promise<Tag | null> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.getTagByName(name)
    }

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.TAGS)
      const store = transaction.objectStore(STORES.TAGS)
      const index = store.index('name')

      const request = index.get(name)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => {
        reject(new Error(`根据名称获取标签失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取所有标签
   * @returns Promise<Tag[]>
   */
  async getTags(): Promise<Tag[]> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.TAGS)
      const store = transaction.objectStore(STORES.TAGS)

      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result || [])
      }

      request.onerror = () => {
        reject(new Error(`获取标签列表失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 更新标签
   * @param id 标签ID
   * @param updates 更新数据
   * @returns Promise<void>
   */
  async updateTag(id: string, updates: Partial<Tag>): Promise<void> {
    await this.ensureInitialized()

    if (this.useFallback) {
      return await fallbackStorageService.updateTag(id, updates)
    }

    const tag = await this.getTag(id)
    if (!tag) {
      throw new Error(`标签不存在: ${id}`)
    }

    const updatedTag = {
      ...tag,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: new Date()
    }

    await this.saveTag(updatedTag)
  }

  /**
   * 删除标签
   * @param id 标签ID
   * @returns Promise<void>
   */
  async deleteTag(id: string): Promise<void> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.TAGS, 'readwrite')
      const store = transaction.objectStore(STORES.TAGS)

      const request = store.delete(id)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`删除标签失败: ${request.error?.message}`))
      }
    })
  }

  // ==================== 设置操作 ====================

  /**
   * 保存设置
   * @param key 设置键
   * @param value 设置值
   * @returns Promise<void>
   */
  async saveSetting(key: string, value: any): Promise<void> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.SETTINGS, 'readwrite')
      const store = transaction.objectStore(STORES.SETTINGS)

      const request = store.put({ key, value, updatedAt: new Date() })

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`保存设置失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 获取设置
   * @param key 设置键
   * @returns Promise<any>
   */
  async getSetting(key: string): Promise<any> {
    await this.ensureInitialized()

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(STORES.SETTINGS)
      const store = transaction.objectStore(STORES.SETTINGS)

      const request = store.get(key)

      request.onsuccess = () => {
        const result = request.result
        resolve(result ? result.value : null)
      }

      request.onerror = () => {
        reject(new Error(`获取设置失败: ${request.error?.message}`))
      }
    })
  }

  // ==================== 工具方法 ====================

  /**
   * 应用书签筛选
   * @param bookmarks 书签数组
   * @param filter 筛选条件
   * @returns 筛选后的书签数组
   */
  private applyBookmarkFilter(bookmarks: Bookmark[], filter: BookmarkFilter): Bookmark[] {
    return bookmarks.filter(bookmark => {
      // 文本搜索
      if (filter.query) {
        const query = filter.query.toLowerCase()
        const searchText = `${bookmark.title} ${bookmark.description || ''} ${bookmark.content || ''}`.toLowerCase()
        if (!searchText.includes(query)) {
          return false
        }
      }

      // 标签筛选
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some(tag => bookmark.tags.includes(tag))
        if (!hasMatchingTag) {
          return false
        }
      }

      // 分类筛选
      if (filter.categories && filter.categories.length > 0) {
        if (!filter.categories.includes(bookmark.category)) {
          return false
        }
      }

      // 类型筛选
      if (filter.type && bookmark.type !== filter.type) {
        return false
      }

      // 日期范围筛选
      if (filter.dateRange) {
        const bookmarkDate = bookmark.createdAt
        if (bookmarkDate < filter.dateRange.start || bookmarkDate > filter.dateRange.end) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 排序书签
   * @param bookmarks 书签数组
   * @param sort 排序选项
   * @returns 排序后的书签数组
   */
  private sortBookmarks(bookmarks: Bookmark[], sort: SortOptions): Bookmark[] {
    return bookmarks.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sort.field) {
        case 'createdAt':
          aValue = a.createdAt.getTime()
          bValue = b.createdAt.getTime()
          break
        case 'updatedAt':
          aValue = a.updatedAt.getTime()
          bValue = b.updatedAt.getTime()
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'category':
          aValue = a.category.toLowerCase()
          bValue = b.category.toLowerCase()
          break
        default:
          return 0
      }

      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }

  /**
   * 清空所有数据
   * @returns Promise<void>
   */
  async clearAllData(): Promise<void> {
    await this.ensureInitialized()

    const storeNames = [STORES.BOOKMARKS, STORES.CATEGORIES, STORES.TAGS, STORES.SETTINGS, STORES.AI_CACHE]

    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(storeNames, 'readwrite')

      let completed = 0
      let hasError = false

      const checkComplete = () => {
        completed++
        if (completed === storeNames.length) {
          if (hasError) {
            reject(new Error('清空数据时发生错误'))
          } else {
            resolve()
          }
        }
      }

      storeNames.forEach(storeName => {
        const store = transaction.objectStore(storeName)
        const request = store.clear()
        request.onsuccess = checkComplete
        request.onerror = () => {
          hasError = true
          checkComplete()
        }
      })
    })
  }

  /**
   * 获取数据库统计信息
   * @returns Promise<object> 统计信息
   */
  async getStats(): Promise<{
    bookmarkCount: number
    categoryCount: number
    tagCount: number
  }> {
    await this.ensureInitialized()

    const [bookmarks, categories, tags] = await Promise.all([
      this.getBookmarks(),
      this.getCategories(),
      this.getTags()
    ])

    return {
      bookmarkCount: bookmarks.length,
      categoryCount: categories.length,
      tagCount: tags.length
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      this.initPromise = null
    }
  }

  // ==================== 通用静态方法 ====================

  /**
   * 静态方法：初始化数据库
   * @returns Promise<void>
   */
  static async initialize(): Promise<void> {
    await indexedDBService.init()
  }

  /**
   * 静态方法：保存数据到指定store
   * @param storeName store名称
   * @param data 要保存的数据
   * @returns Promise<void>
   */
  static async save<T extends { id: string }>(storeName: string, data: T): Promise<void> {
    await indexedDBService.ensureInitialized()

    if (indexedDBService.useFallback) {
      // 使用Chrome Storage作为降级存储
      const key = `${storeName}_${data.id}`
      await chrome.storage.local.set({ [key]: data })
      return
    }

    return new Promise((resolve, reject) => {
      const transaction = indexedDBService.getTransaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)

      const request = store.put(data)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`保存数据到${storeName}失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 静态方法：从指定store获取数据
   * @param storeName store名称
   * @param id 数据ID
   * @returns Promise<T | null>
   */
  static async get<T>(storeName: string, id: string): Promise<T | null> {
    await indexedDBService.ensureInitialized()

    if (indexedDBService.useFallback) {
      // 使用Chrome Storage作为降级存储
      const key = `${storeName}_${id}`
      const result = await chrome.storage.local.get(key)
      return result[key] || null
    }

    return new Promise((resolve, reject) => {
      const transaction = indexedDBService.getTransaction([storeName])
      const store = transaction.objectStore(storeName)

      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => {
        reject(new Error(`从${storeName}获取数据失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 静态方法：从指定store获取所有数据
   * @param storeName store名称
   * @returns Promise<T[]>
   */
  static async getAll<T>(storeName: string): Promise<T[]> {
    await indexedDBService.ensureInitialized()

    if (indexedDBService.useFallback) {
      // 使用Chrome Storage作为降级存储
      const result = await chrome.storage.local.get(null)
      const items: T[] = []

      for (const [key, value] of Object.entries(result)) {
        if (key.startsWith(`${storeName}_`)) {
          items.push(value as T)
        }
      }

      return items
    }

    return new Promise((resolve, reject) => {
      const transaction = indexedDBService.getTransaction([storeName])
      const store = transaction.objectStore(storeName)

      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result || [])
      }

      request.onerror = () => {
        reject(new Error(`从${storeName}获取所有数据失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 静态方法：从指定store删除数据
   * @param storeName store名称
   * @param id 数据ID
   * @returns Promise<void>
   */
  static async delete(storeName: string, id: string): Promise<void> {
    await indexedDBService.ensureInitialized()

    if (indexedDBService.useFallback) {
      // 使用Chrome Storage作为降级存储
      const key = `${storeName}_${id}`
      await chrome.storage.local.remove(key)
      return
    }

    return new Promise((resolve, reject) => {
      const transaction = indexedDBService.getTransaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)

      const request = store.delete(id)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`从${storeName}删除数据失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 静态方法：清空指定store的所有数据
   * @param storeName store名称
   * @returns Promise<void>
   */
  static async clear(storeName: string): Promise<void> {
    await indexedDBService.ensureInitialized()

    if (indexedDBService.useFallback) {
      // 使用Chrome Storage作为降级存储
      const result = await chrome.storage.local.get(null)
      const keysToRemove: string[] = []

      for (const key of Object.keys(result)) {
        if (key.startsWith(`${storeName}_`)) {
          keysToRemove.push(key)
        }
      }

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove)
      }
      return
    }

    return new Promise((resolve, reject) => {
      const transaction = indexedDBService.getTransaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)

      const request = store.clear()

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = () => {
        reject(new Error(`清空${storeName}失败: ${request.error?.message}`))
      }
    })
  }
}

// 导出单例实例
export const indexedDBService = new IndexedDBService()