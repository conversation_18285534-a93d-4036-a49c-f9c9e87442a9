# AI模型测试功能使用指南

## 功能概述

在AI集成页面新增了模型测试功能，可以直接测试已配置的AI模型是否能正常工作。

## 新增功能

### 1. 模型测试按钮 💬
- **位置**: AI集成页面的每个提供商卡片中
- **图标**: MessageCircle（对话气泡）
- **功能**: 发送"你好"测试消息到选定的AI模型

### 2. 测试结果显示 📊
- **成功状态**: 绿色背景，显示AI的实际回复
- **失败状态**: 红色背景，显示错误信息
- **时间戳**: 显示测试执行时间

## 使用步骤

### 1. 重新加载扩展
1. 打开Chrome扩展页面 (chrome://extensions/)
2. 找到Universe Bag扩展
3. 点击"重新加载"按钮（🔄）

### 2. 进行模型测试
1. 访问"AI集成"页面
2. 确保提供商已启用且已选择模型
3. 点击💬按钮进行模型测试
4. 查看测试结果

## 技术改进

### 1. 调试功能增强
- aiProviderService添加了详细的调试日志
- 显示提供商查找过程和结果

### 2. 统一存储访问
- 修复了aiProviderService和aiIntegrationService的数据不一致问题
- 使用相同的ChromeStorageService访问存储

### 3. 新增测试类型
- aiChatService支持'test'类型的文本生成
- 专门的测试提示词优化

## 预期效果

- **快速诊断**: 确定AI模型是否能正常工作
- **验证配置**: 确认提供商和模型配置正确性
- **调试辅助**: 通过控制台日志查看详细过程
- **用户友好**: 直观的状态显示

## 故障排除

如果测试失败，请检查：
1. 提供商是否已启用
2. 是否已选择模型
3. 网络连接是否正常
4. API密钥是否正确
5. 查看浏览器控制台的详细错误信息

这个功能将帮助快速诊断AI推荐功能的问题！
