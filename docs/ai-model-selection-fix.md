# AI模型选择和调用一致性修复

## 问题描述

在AI集成功能的模型测试过程中发现了一个严重的问题：用户在界面上选择的模型与实际API调用时使用的模型不一致。

### 具体表现

从控制台日志可以看出：
- **用户选择的模型**: `qwen/qwen3-30b-a3b`（从日志"模型选择已保存: lm-studio_1755596350020 -> qwen/qwen3-30b-a3b"可以看出）
- **实际调用的模型**: `deepseek-r1-distill-qwen-32b`（从错误信息"Failed to load model \"deepseek-r1-distill-qwen-32b\""可以看出）

### 错误日志示例

```
模型选择已保存: lm-studio_1755596350020 -> qwen/qwen3-30b-a3b
开始测试模型对话: Object
模型测试失败: Error: AI生成文本失败: Error: AI API调用失败: HTTP 404: {
    "error": {
        "message": "Failed to load model \"deepseek-r1-distill-qwen-32b\"",
        "type": "invalid_request_error",
        "param": "model",
        "code": "model_not_found"
    }
}
```

## 根本原因分析

问题出现在 `src/services/aiChatService.ts` 的 `generateText` 方法中：

### 原始代码问题

```typescript
async generateText(request: AIGenerateRequest): Promise<AIGenerateResponse> {
  // 问题：完全忽略了 request.context 参数
  const defaultModel = await this.getDefaultModel()
  // ... 直接使用默认模型，而不是用户选择的模型
}
```

### 调用流程分析

1. **用户操作**: 在AI集成页面选择模型 `qwen/qwen3-30b-a3b`
2. **模型保存**: 正确保存到 `ai_selected_models` 设置中
3. **测试请求**: 发送包含 `context: {providerId, modelId}` 的请求
4. **问题发生**: `aiChatService.generateText` 忽略 `context` 参数
5. **错误结果**: 使用默认模型 `deepseek-r1-distill-qwen-32b` 而不是用户选择的模型

## 修复方案

### 修改 `aiChatService.generateText` 方法

在 `src/services/aiChatService.ts` 中修改模型选择逻辑：

```typescript
async generateText(request: AIGenerateRequest): Promise<AIGenerateResponse> {
  try {
    console.log('开始生成AI文本:', request.generationType)
    
    // 优先使用context中指定的模型，如果没有则使用默认模型
    let targetModel: {providerId: string, modelId: string} | null = null
    
    if (request.context?.providerId && request.context?.modelId) {
      // 使用context中指定的模型（用于模型测试等场景）
      targetModel = {
        providerId: request.context.providerId,
        modelId: request.context.modelId
      }
      console.log('使用context指定的模型:', targetModel)
    } else {
      // 使用默认模型
      targetModel = await this.getDefaultModel()
      if (!targetModel) {
        throw new Error('未找到可用的AI模型，请先在"默认AI模型"页面配置模型')
      }
      console.log('使用默认模型:', targetModel)
    }
    
    // ... 后续使用 targetModel 而不是 defaultModel
  }
}
```

### 关键改进点

1. **优先级处理**: 优先使用 `context` 中指定的模型
2. **向后兼容**: 当没有 `context` 时回退到默认模型
3. **调试信息**: 增加清晰的日志记录，便于问题排查
4. **类型安全**: 保持原有的类型定义不变

## 测试验证

### 创建专门的测试用例

在 `tests/aiChatService.modelSelection.test.ts` 中创建了完整的测试套件：

```typescript
describe('AIChatService - 模型选择和调用一致性修复', () => {
  it('应该使用context中指定的模型进行测试调用', async () => {
    const request = {
      prompt: '你好，请简单回复一下确认你能正常工作。',
      generationType: 'test' as const,
      context: {
        providerId: 'lm-studio_1755596350020',
        modelId: 'qwen/qwen3-30b-a3b'
      },
      maxLength: 100
    }

    const result = await aiChatService.generateText(request)

    // 验证使用了正确的模型
    expect(mockAiProviderService.generateText).toHaveBeenCalledWith({
      providerId: 'lm-studio_1755596350020',
      modelId: 'qwen/qwen3-30b-a3b', // 用户选择的模型
      // ...
    })
  })
})
```

### 测试结果

所有测试用例都通过：
- ✅ 应该使用context中指定的模型进行测试调用
- ✅ 应该在没有context时回退到默认模型
- ✅ 应该优先使用context中的模型而不是默认模型
- ✅ 应该在context不完整时使用默认模型
- ✅ 应该记录正确的调试信息

## 影响范围

### 修复的功能

1. **模型测试**: AI集成页面的模型测试功能
2. **一致性保证**: 确保用户选择的模型与实际调用的模型一致
3. **错误减少**: 避免因模型不匹配导致的API调用失败

### 不受影响的功能

1. **默认模型**: 正常的AI文本生成仍使用默认模型
2. **向后兼容**: 现有的调用方式不受影响
3. **其他AI功能**: 标签生成、分类生成等功能保持不变

## 部署说明

1. **无需数据迁移**: 修复不涉及数据结构变更
2. **无需配置更新**: 用户无需重新配置
3. **即时生效**: 重新加载扩展后立即生效

## 验证步骤

修复后，用户可以通过以下步骤验证：

1. 打开AI集成页面
2. 选择一个可用的模型
3. 点击"测试模型"按钮
4. 检查控制台日志，确认：
   - "使用context指定的模型" 显示正确的模型ID
   - "使用模型" 显示与用户选择一致的模型ID
   - 没有出现模型不匹配的错误

## 总结

这次修复解决了AI集成功能中一个关键的用户体验问题，确保了用户界面操作与后端API调用的一致性。通过优先使用context参数中的模型信息，同时保持向后兼容性，我们提供了一个稳定可靠的解决方案。
